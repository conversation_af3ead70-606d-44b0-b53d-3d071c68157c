# 咖啡工作流配置

## 支持的咖啡类型

- **美式咖啡** - 单臂操作，简单高效
- **拿铁咖啡** - 双臂协调，支持心形、叶子、郁金香、天鹅拉花
- **卡布奇诺** - 双臂协调，心形拉花

## 工作流文件

系统根据咖啡类型和拉花类型自动选择对应的工作流配置文件：

```
share/config/workflows/
├── americano.json              # 美式咖啡
├── latte_heart.json           # 拿铁-心形
├── latte_leaf.json            # 拿铁-叶子
├── latte_tulip.json           # 拿铁-郁金香
├── latte_swan.json            # 拿铁-天鹅
└── cappuccino_heart.json      # 卡布奇诺-心形
```

## 工作流结构

### 美式咖啡 (3步骤)
1. **取杯子** - 单臂操作
2. **接咖啡** - 单臂操作
3. **交付咖啡** - 单臂操作

### 拿铁/卡布奇诺 (5步骤)
1. **取杯子** - 左臂操作
2. **接咖啡和奶泡** - 双臂并行协调
3. **拉花** - 双臂协调拉花
4. **交付和清理** - 双臂并行操作
5. **回到准备位置** - 双臂并行回位

## 制作流程示例：拿铁-心形

```mermaid
sequenceDiagram
    participant 用户
    participant 咖啡制作系统
    participant 左臂
    participant 右臂
    participant 咖啡机
    participant 杯子分配器

    用户->>咖啡制作系统: 制作拿铁-心形
    咖啡制作系统->>咖啡制作系统: 选择latte_heart工作流

    Note over 咖啡制作系统: Step 1: 取杯子
    咖啡制作系统->>左臂: 移动到准备位置
    咖啡制作系统->>左臂: 移动到杯子出口
    咖啡制作系统->>杯子分配器: 分配杯子
    咖啡制作系统->>左臂: 移动到准备位置

    Note over 咖啡制作系统: Step 2: 接咖啡和奶泡 (并行)
    par 左臂处理咖啡
        咖啡制作系统->>左臂: 移动到咖啡出口
        咖啡制作系统->>咖啡机: 分配咖啡
        Note over 咖啡制作系统: 等待30秒
        咖啡制作系统->>左臂: 移动到准备位置
    and 右臂处理奶泡
        咖啡制作系统->>右臂: 移动到清洁位置
        Note over 咖啡制作系统: 清洗消毒奶容器
        咖啡制作系统->>右臂: 移动到奶出口
        咖啡制作系统->>咖啡机: 分配奶泡
        Note over 咖啡制作系统: 等待20秒
        咖啡制作系统->>右臂: 摇奶(15圈)
    end

    Note over 咖啡制作系统: Step 3: 拉花
    par 移动到拉花位置
        咖啡制作系统->>左臂: 移动到拉花位置
        and
        咖啡制作系统->>右臂: 移动到拉花位置
    end
    par 协同拉花
        咖啡制作系统->>左臂: 执行心形拉花
        and
        咖啡制作系统->>右臂: 执行心形拉花
    end

    Note over 咖啡制作系统: Step 4: 交付和清理 (并行)
    par 交付咖啡
        咖啡制作系统->>左臂: 交付咖啡
    and 清理工作
        咖啡制作系统->>右臂: 倾倒剩余奶泡
        咖啡制作系统->>右臂: 移动到清洁位置
        Note over 咖啡制作系统: 清洗奶容器
    end

    Note over 咖啡制作系统: Step 5: 回到准备位置
    par 双臂回位
        咖啡制作系统->>左臂: 移动到home位置
        and
        咖啡制作系统->>右臂: 移动到home位置
    end

    咖啡制作系统->>用户: 拿铁-心形制作完成
```

## 关键特性

- **双臂协调**：左右臂并行操作，提高效率
- **清洁消毒**：接奶前自动清洗消毒奶容器
- **精确控制**：摇奶圈数、等待时间等参数化控制
- **模块化设计**：每个步骤独立，便于维护和调试
